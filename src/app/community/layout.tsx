import type { Metadata, Viewport } from 'next';

// Import from @next/third-parties when ready to implement
// import { GoogleAnalytics } from '@next/third-parties/google';

import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

import { AppSidebar } from '@/components/community/app-sidebar';
import CommunityClientLayout from '@/components/community/layout/community-client-layout';
import '../globals.css';

export const metadata: Metadata = {
  title: 'SODAMM - 커뮤니티',
  description: 'SODAMM 커뮤니티에서 다양한 주제로 소통해보세요.',
  openGraph: {
    title: 'SODAMM - 소통하는 담벼락',
    description:
      '사람들이 자유롭게 생각을 나누고 소통할 수 있는 현대적인 커뮤니티 플랫폼',
    type: 'website',
    locale: 'ko_KR',
    siteName: 'SODAMM',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SODAMM - 소통하는 담벼락',
    description:
      '사람들이 자유롭게 생각을 나누고 소통할 수 있는 현대적인 커뮤니티 플랫폼',
  },
  // viewport: 'width=device-width, initial-scale=1',
  // themeColor: '#ffffff',
};

export function generateViewport(): Viewport {
  return {
    width: 'device-width',
    initialScale: 1,
    themeColor: '#ffffff',
  };
}

export default async function CommunityLayout({
  children,
  modal,
}: Readonly<{
  children: React.ReactNode;
  modal?: React.ReactNode;
}>) {
  return (
    <>
      <SidebarProvider
        defaultOpen={true}
        style={
          {
            '--sidebar-width': '14rem',
            '--sidebar-width-icon': '12rem',
          } as React.CSSProperties
        }
      >
        <AppSidebar />
        <SidebarInset>
          <CommunityClientLayout>{children}</CommunityClientLayout>
          {modal}
        </SidebarInset>
      </SidebarProvider>
    </>
  );
}
