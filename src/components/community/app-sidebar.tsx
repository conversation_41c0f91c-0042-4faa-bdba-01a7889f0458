'use client';

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import { Label } from '@radix-ui/react-label';
import {
  BellRing,
  House,
  LayoutGrid,
  LogIn,
  LucideIcon,
  Megaphone,
  Scan,
  Search,
  UserRoundCog,
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import { AppSidebarUser } from './app-sidebar-user';

interface SidebarMenuItemType {
  icon: LucideIcon;
  title: string;
  url: string;
  isEnabled: boolean;
  separator?: boolean;
  link?: string;
}

const items: SidebarMenuItemType[] = [
  {
    icon: House,
    title: 'HOME',
    url: '/community',
    isEnabled: true,
  },
  {
    icon: Megaphone,
    title: 'NEWS',
    url: '/community/news',
    isEnabled: true,
  },
  {
    icon: Scan,
    title: 'Feed',
    url: '/community/feed',
    isEnabled: true,
  },
  {
    icon: Search,
    title: 'Search',
    url: '/community/search',
    isEnabled: false,
  },
  {
    icon: BellRing,
    title: 'Activity',
    url: '/community/me/activity',
    isEnabled: false,
  },
  {
    icon: UserRoundCog,
    title: 'Profile',
    url: '/me',
    isEnabled: true,
  },
  {
    icon: LayoutGrid,
    title: 'More',
    url: '/community/more',
    isEnabled: false,
  },
  // {
  //   icon: Plus,
  //   title: 'Separator',
  //   url: '',
  //   isEnabled: true,
  //   separator: true,
  // },
  // {
  //   icon: CheckCheck,
  //   title: 'Job',
  //   url: '/job',
  //   isEnabled: false,
  //   link: 'https://job.sodamm.com/',
  // },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session, status } = useSession();
  const { setOpenMobile, isMobile } = useSidebar();
  const { theme } = useTheme();
  const handleMenuClick = () => {
    setOpenMobile(false);
  };

  return (
    <>
      <Sidebar
        collapsible="offcanvas"
        variant="floating"
        className="var(--background)"
        {...props}
      >
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <div className="flex flex-row gap-3">
                  <Image
                    src={
                      theme === 'dark'
                        ? '/logo_only_white.svg'
                        : '/logo_only_black.svg'
                    }
                    alt="logo"
                    width={30}
                    height={20}
                  />
                </div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent className="justify-center">
          <SidebarMenu className="gap-y-2">
            {items.map((item) => {
              if (item.separator) {
                return (
                  <hr
                    key={item.title}
                    className="border-border my-2 border-t"
                  />
                );
              }
              return (
                <SidebarMenuItem
                  key={item.title}
                  className="w-full"
                >
                  <SidebarMenuButton
                    disabled={!item.isEnabled}
                    className="w-full"
                    tooltip={!isMobile ? item.title : undefined}
                  >
                    <Link
                      href={item.url}
                      className="hover:text-primary flex w-full cursor-pointer flex-row items-center gap-3 pl-1 transition-colors"
                      scroll={false}
                      prefetch={false}
                      onClick={handleMenuClick}
                    >
                      <item.icon className="size-6 flex-shrink-0" />
                      <span className="text-sm font-bold">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                {status === 'loading' ? (
                  <div className="flex items-center gap-2">
                    <span className="text-sm">로딩 중...</span>
                  </div>
                ) : session ? (
                  <Link
                    href="/me"
                    onClick={handleMenuClick}
                  >
                    <AppSidebarUser user={session!.user} />
                  </Link>
                ) : (
                  <Link
                    href="/auth/login"
                    scroll={false}
                    prefetch={false}
                    onClick={handleMenuClick}
                  >
                    <div className="flex flex-row items-center justify-start gap-2">
                      <LogIn className="size-5" />
                      <span>
                        <Label className="text-sm font-bold">Sign In</Label>
                      </span>
                    </div>
                  </Link>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
    </>
  );
}
