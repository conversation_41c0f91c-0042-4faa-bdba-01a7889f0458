'use client';

import { useRouter } from 'next/navigation';
import { Button } from './ui/button';

interface BackButtonProps {
  className?: string;
  children?: React.ReactNode;
}

export default function BackButton({ className, children }: BackButtonProps) {
  const router = useRouter();

  return (
    <Button
      variant="ghost" // 원하는 버튼 스타일로 변경 가능합니다.
      onClick={() => router.back()}
      className={className}
    >
      {children || '← 뒤로가기'}
    </Button>
  );
}
