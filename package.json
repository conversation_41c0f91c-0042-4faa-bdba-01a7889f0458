{"name": "sodamm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "eslint --fix './src/**/*.{js,jsx,ts,tsx}' && prettier --write .", "format:check": "eslint './src/**/*.{js,jsx,ts,tsx}' && prettier --check .", "mcp:browser-tools": "npx @agentdeskai/browser-tools-server@1.2.0", "ui:add": "npx shadcn@latest add", "seed:countries": "tsx prisma/seed.ts", "analyze": "ANALYZE=true next build", "analyze:client": "ANALYZE=true BUNDLE_ANALYZE=client next build", "analyze:server": "ANALYZE=true BUNDLE_ANALYZE=server next build"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@next/third-parties": "^15.3.0", "@prisma/adapter-pg": "^6.5.0", "@prisma/client": "^6.6.0", "@prisma/extension-accelerate": "^1.3.0", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.3", "@supabase/supabase-js": "^2.49.4", "@tiptap/core": "^2.11.9", "@tiptap/extension-code-block-lowlight": "^2.11.9", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-highlight": "^2.11.9", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/extension-youtube": "^2.11.9", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.12.0", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "highlight.js": "^11.11.1", "immer": "^10.1.1", "jsdom": "^26.1.0", "lowlight": "^3.3.0", "lucide-react": "^0.484.0", "next": "15.2.4", "next-auth": "5.0.0-beta.25", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "nuqs": "^2.4.1", "prosemirror-model": "^1.25.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.39.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "remark-breaks": "^4.0.0", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "web-vitals": "^5.0.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.1.8", "@next/eslint-plugin-next": "^15.3.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/jsdom": "^21.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-turbo": "^2.5.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.6.0", "tailwindcss": "^4.1.4", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.3", "typescript": "^5", "typescript-eslint": "^8.30.1", "webpack-bundle-analyzer": "^4.10.2"}}